# Cremote MCP Tools - Quick Reference

## Tool Names
- `web_navigate_cremotemcp` - Navigate to URLs
- `web_interact_cremotemcp` - Interact with elements
- `web_extract_cremotemcp` - Extract page data
- `web_screenshot_cremotemcp` - Take screenshots
- `web_manage_tabs_cremotemcp` - Manage browser tabs
- `web_iframe_cremotemcp` - Switch iframe context
- `file_upload_cremotemcp` - Upload files to container
- `file_download_cremotemcp` - Download files from container
- `console_logs_cremotemcp` - Get browser console logs
- `console_command_cremotemcp` - Execute console commands

## Essential Parameters

### web_navigate_cremotemcp
```yaml
url: "https://example.com"        # Required
screenshot: true                  # Optional, default false
timeout: 10                      # Optional, default 5 seconds
```

### web_interact_cremotemcp
```yaml
action: "click"                  # Required: click|fill|submit|upload
selector: "button.submit"        # Required: CSS selector
value: "text to fill"           # Required for fill/upload actions
timeout: 10                     # Optional, default 5 seconds
```

## Common Patterns

### Navigate + Screenshot
```yaml
web_navigate_cremotemcp:
  url: "https://example.com"
  screenshot: true
```

### Fill Form Field
```yaml
web_interact_cremotemcp:
  action: "fill"
  selector: "input[name='email']"
  value: "<EMAIL>"
```

### Click Button
```yaml
web_interact_cremotemcp:
  action: "click"
  selector: "button#submit"
```

### Submit Form
```yaml
web_interact_cremotemcp:
  action: "submit"
  selector: "form"
```

### Upload File
```yaml
web_interact_cremotemcp:
  action: "upload"
  selector: "input[type='file']"
  value: "/path/to/file.pdf"
```

### Upload File to Container
```yaml
file_upload_cremotemcp:
  local_path: "/home/<USER>/document.pdf"
  container_path: "/tmp/upload.pdf"
```

### Download File from Container
```yaml
file_download_cremotemcp:
  container_path: "/tmp/downloaded.pdf"
  local_path: "/home/<USER>/Downloads/file.pdf"
```

### Get Console Logs
```yaml
console_logs_cremotemcp:
  clear: true
```

### Execute Console Command
```yaml
console_command_cremotemcp:
  command: "document.title"
  timeout: 10
```

## Best CSS Selectors

✅ **Good:**
- `#unique-id`
- `input[name='fieldname']`
- `button.primary-submit`
- `form#login-form`

❌ **Avoid:**
- `div` (too generic)
- `input` (too broad)
- `:nth-child(3)` (fragile)

## Typical Workflow

1. **Navigate** to target page
2. **Fill** required form fields
3. **Click** submit buttons
4. **Take screenshots** for verification
5. **Navigate** to next page if needed

## Error Handling

- **Element not found**: Check CSS selector
- **Timeout**: Increase timeout parameter
- **Navigation failed**: Verify URL accessibility

## Screenshots

Screenshots are automatically saved to `/tmp/navigate-{timestamp}.png` when requested.
